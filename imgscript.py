"""
Robust MCQ image extractor + mapper (fallback clustering if OCR mapping fails)

Requirements:
  pip install opencv-python pytesseract numpy

Usage:
  - Put page_1.png and mapping.json in same folder
  - Run: python extract_and_map_mcq.py
  - Outputs in ./output_mcq/
"""

import cv2
import numpy as np
import pytesseract
import json
import os
from statistics import median

# ----------------------------
# CONFIG
# ----------------------------
INPUT_IMAGE = "page_1.png"
MAPPING_JSON = "mapping.json"
OUTPUT_DIR = "output_mcq"

# region detection thresholds (tune if needed)
MIN_AREA = 1200
MIN_WIDTH = 30
MIN_HEIGHT = 30

# morphological kernel for merging nearby content
MORPH_K = (12, 12)

# clustering x-gap threshold as fraction of page width
CLUSTER_X_GAP_FRAC = 0.12

os.makedirs(OUTPUT_DIR, exist_ok=True)

# ----------------------------
# helper: safe native conversion
# ----------------------------
def native(v):
    if isinstance(v, (np.integer,)):
        return int(v)
    if isinstance(v, (np.floating,)):
        return float(v)
    if isinstance(v, (np.bool_, bool)):
        return bool(v)
    if isinstance(v, (list, tuple)):
        return [native(x) for x in v]
    if isinstance(v, dict):
        return {k: native(x) for k, x in v.items()}
    return v

# ----------------------------
# load mapping JSON
# ----------------------------
with open(MAPPING_JSON, "r") as f:
    mapping_data = json.load(f)

# build question -> sorted option list
q_to_options = {}
for ent in mapping_data:
    q = int(ent["question_number"])
    o = ent.get("option_number")
    if q not in q_to_options:
        q_to_options[q] = []
    # option_number might be null for the question image itself; we still accept it
    q_to_options[q].append(o)
# Sort the option numbers for consistent assignment (None will be last)
for q in q_to_options:
    q_to_options[q] = sorted(q_to_options[q], key=lambda x: (x is None, x))

# ----------------------------
# load image
# ----------------------------
img = cv2.imread(INPUT_IMAGE)
if img is None:
    raise SystemExit(f"Could not open image '{INPUT_IMAGE}'")
H, W = img.shape[:2]
gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

# ----------------------------
# layout segmentation (morphology + contours)
# ----------------------------
_, th = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
kernel = cv2.getStructuringElement(cv2.MORPH_RECT, MORPH_K)
closed = cv2.morphologyEx(th, cv2.MORPH_CLOSE, kernel)

contours, _ = cv2.findContours(closed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

regions = []
vis = img.copy()
for i, c in enumerate(contours):
    x, y, w, h = cv2.boundingRect(c)
    if w < MIN_WIDTH or h < MIN_HEIGHT or w*h < MIN_AREA:
        continue
    cx = x + w/2
    cy = y + h/2
    area = w*h
    ar = w / (h + 1e-9)
    # compute simple features
    crop_gray = gray[y:y+h, x:x+w]
    lap_var = float(cv2.Laplacian(crop_gray, cv2.CV_64F).var())
    nonwhite = float(np.sum(crop_gray < 245) / (crop_gray.size + 1e-9))
    # Always save the crop as region_{id}.png for debugging/mapping
    crop_fname = f"region_{i}.png"
    cv2.imwrite(os.path.join(OUTPUT_DIR, crop_fname), img[y:y+h, x:x+w])

    region = {
        "id": int(i),
        "bbox": [int(x), int(y), int(w), int(h)],
        "center": [float(cx), float(cy)],
        "area": float(area),
        "aspect_ratio": float(ar),
        "lap_var": lap_var,
        "nonwhite": nonwhite,
        "crop": crop_fname
    }
    regions.append(region)

    # draw rectangle for debug
    cv2.rectangle(vis, (x, y), (x + w, y + h), (0, 255, 0), 1)

# sort regions by center_y (reading order)
regions = sorted(regions, key=lambda r: (r["center"][1], r["center"][0]))
cv2.imwrite(os.path.join(OUTPUT_DIR, "regions_vis.png"), vis)

# ----------------------------
# OCR tokens (pytesseract)
# ----------------------------
ocr = []
try:
    data = pytesseract.image_to_data(gray, output_type=pytesseract.Output.DICT)
    for i, txt in enumerate(data["text"]):
        if not txt or txt.strip() == "":
            continue
        # keep raw token text and bbox; we'll try to interpret numeric tokens only
        token = {
            "text_raw": txt.strip(),
            "text": txt.strip(),
            "bbox": [int(data["left"][i]), int(data["top"][i]),
                     int(data["width"][i]), int(data["height"][i])],
            "center": [int(data["left"][i] + data["width"][i]/2),
                       int(data["top"][i] + data["height"][i]/2)]
        }
        ocr.append(token)
except Exception as e:
    print("OCR failed or pytesseract not installed:", e)
    ocr = []

# helper: try to find a question number in OCR tokens, with digit merging fallback
def find_question_token(qnum, ocr_tokens, horiz_tol=20, vert_tol=8):
    s = str(qnum)
    # Exact token match (allow punctuation)
    for t in ocr_tokens:
        ttxt = ''.join(ch for ch in t["text"] if ch.isdigit())
        if ttxt == s:
            return t
    # Merge adjacent digit tokens: look for sequences that concatenate to s
    n = len(ocr_tokens)
    for i in range(n):
        t = ocr_tokens[i]
        if not any(ch.isdigit() for ch in t["text"]):
            continue
        concat = ''.join(ch for ch in t["text"] if ch.isdigit())
        x0, y0, w0, h0 = t["bbox"]
        j = i + 1
        while j < n:
            tj = ocr_tokens[j]
            # require roughly same text line
            if abs(tj["bbox"][1] - y0) > vert_tol:
                break
            # require small horizontal gap
            if tj["bbox"][0] - (x0 + w0) > horiz_tol + tj["bbox"][2]:
                break
            concat += ''.join(ch for ch in tj["text"] if ch.isdigit())
            if concat == s:
                # return a merged bbox token
                left = min(t["bbox"][0], tj["bbox"][0])
                top = min(t["bbox"][1], tj["bbox"][1])
                right = max(t["bbox"][0] + t["bbox"][2], tj["bbox"][0] + tj["bbox"][2])
                bottom = max(t["bbox"][1] + t["bbox"][3], tj["bbox"][1] + tj["bbox"][3])
                merged = {
                    "text_raw": concat,
                    "text": concat,
                    "bbox": [int(left), int(top), int(right - left), int(bottom - top)],
                    "center": [int((left + right)/2), int((top + bottom)/2)]
                }
                return merged
            j += 1
    return None

# Precompute numeric tokens for debugging convenience
numeric_tokens = []
for t in ocr:
    digits = ''.join([c for c in t['text'] if c.isdigit()])
    if digits:
        numeric_tokens.append({
            "text": digits,
            "bbox": t["bbox"],
            "center": t["center"]
        })

# ----------------------------
# Cluster regions by X (columns)
# ----------------------------
# sort candidate regions by center_x then group when gap > threshold
regions_by_x = sorted(regions, key=lambda r: r["center"][0])
x_gap_thresh = int(W * CLUSTER_X_GAP_FRAC)
clusters = []
current = []
for i, r in enumerate(regions_by_x):
    if not current:
        current = [r]
    else:
        prev = current[-1]
        gap = abs(r["center"][0] - prev["center"][0])
        if gap <= x_gap_thresh:
            current.append(r)
        else:
            clusters.append(current)
            current = [r]
if current:
    clusters.append(current)

# compute cluster metadata (median_y, size, bounding box)
cluster_metas = []
for ci, cl in enumerate(clusters):
    cx = int(median([r["center"][0] for r in cl]))
    cy = int(median([r["center"][1] for r in cl]))
    bbox_left = min(r["bbox"][0] for r in cl)
    bbox_top = min(r["bbox"][1] for r in cl)
    bbox_right = max(r["bbox"][0] + r["bbox"][2] for r in cl)
    bbox_bottom = max(r["bbox"][1] + r["bbox"][3] for r in cl)
    cluster_metas.append({
        "cluster_id": ci,
        "n_regions": len(cl),
        "median_center": [cx, cy],
        "bbox": [bbox_left, bbox_top, bbox_right - bbox_left, bbox_bottom - bbox_top],
        "regions": cl
    })

# Save a debug cluster visualization
cluster_vis = img.copy()
colors = [(255,0,0),(0,255,0),(0,0,255),(255,255,0),(255,0,255),(0,255,255)]
for meta in cluster_metas:
    left,top,wc,hc = meta["bbox"]
    color = colors[meta["cluster_id"] % len(colors)]
    cv2.rectangle(cluster_vis, (left, top), (left+wc, top+hc), color, 2)
    cv2.putText(cluster_vis, f"c{meta['cluster_id']} n={meta['n_regions']}",
                (left, top-6), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
cv2.imwrite(os.path.join(OUTPUT_DIR, "clusters_vis.png"), cluster_vis)

# ----------------------------
# Map questions -> cluster/regions
# ----------------------------
# Build question list with optional OCR positions
questions = []
for qnum, opts in q_to_options.items():
    token = find_question_token(qnum, ocr)
    qcenter = token["center"] if token else None
    qbox = token["bbox"] if token else None
    questions.append({"q": qnum, "options": opts, "center": qcenter, "bbox": qbox})

# Sort questions: those with known center by Y, then unknown by q number
questions_known = [q for q in questions if q["center"] is not None]
questions_unknown = [q for q in questions if q["center"] is None]
questions_known = sorted(questions_known, key=lambda q: q["center"][1])
questions_unknown = sorted(questions_unknown, key=lambda q: q["q"])
questions_sorted = questions_known + questions_unknown

used_clusters = set()
mapped_results = []

# utility: select best cluster for question
def choose_cluster_for_question(qcenter, required_n):
    # Try clusters whose median_center_y is >= qcenter_y (below the question), prefer minimal distance
    best = None
    best_score = 1e18
    qy = qcenter[1] if qcenter is not None else None
    for meta in cluster_metas:
        if meta["cluster_id"] in used_clusters:
            continue
        if meta["n_regions"] < required_n:
            continue
        my = meta["median_center"][1]
        if qy is not None:
            if my < qy - 30:
                # cluster is above question, skip
                continue
            score = abs(my - qy)
        else:
            # no qcenter: choose topmost unused cluster with enough regions (minimize median y)
            score = my
        if score < best_score:
            best_score = score
            best = meta
    return best

# Map each question in sorted order
for qentry in questions_sorted:
    qnum = qentry["q"]
    opts = [o for o in qentry["options"] if o is not None]  # ignore None if present
    needed = len(opts)
    selected_cluster = None

    if qentry["center"] is not None:
        selected_cluster = choose_cluster_for_question(qentry["center"], needed)

    # Fallback: if none selected via OCR, pick next unused cluster with enough regions (top-down)
    if selected_cluster is None:
        candidates = [c for c in cluster_metas if c["cluster_id"] not in used_clusters and c["n_regions"] >= needed]
        candidates = sorted(candidates, key=lambda c: c["median_center"][1])
        selected_cluster = candidates[0] if candidates else None

    if selected_cluster is None:
        # as last resort try any region list that has enough regions
        all_cands = [c for c in cluster_metas if c["n_regions"] >= needed]
        selected_cluster = all_cands[0] if all_cands else None

    if selected_cluster is None:
        # cannot map this question
        print(f"[WARN] Could not find cluster / regions for question {qnum} expecting {needed} options")
        continue

    # mark cluster used
    used_clusters.add(selected_cluster["cluster_id"])

    # pick exact regions from the cluster: sort by center_y (top->bottom)
    regs = sorted(selected_cluster["regions"], key=lambda r: (r["center"][1], r["center"][0]))
    # If cluster has more regions than needed, pick the top 'needed' (or smallest distance to question center)
    if qentry["center"] is not None:
        regs = sorted(regs, key=lambda r: abs(r["center"][1] - qentry["center"][1]))
    regs = regs[:needed]

    # assign options by ascending option number (user mapping order)
    opts_sorted = sorted(opts)
    for opt_idx, opt in enumerate(opts_sorted):
        if opt_idx >= len(regs):
            break
        r = regs[opt_idx]
        src = os.path.join(OUTPUT_DIR, r["crop"])
        if not os.path.exists(src):
            # crop file missing (shouldn't happen), create it now
            x,y,wc,hc = r["bbox"]
            cv2.imwrite(src, img[y:y+hc, x:x+wc])
        out_name = f"Q{qnum}_O{opt}.png"
        dst = os.path.join(OUTPUT_DIR, out_name)
        cv2.imwrite(dst, cv2.imread(src))
        mapped_results.append({
            "question_number": int(qnum),
            "option_number": int(opt),
            "image_file": out_name,
            "region_id": int(r["id"]),
            "region_bbox": r["bbox"]
        })

# ----------------------------
# Write summary (native types)
# ----------------------------
summary = {
    "regions": [native(r) for r in regions],
    "clusters": [native(cm) for cm in cluster_metas],
    "ocr_numeric_tokens": [native(t) for t in numeric_tokens],
    "mapped_results": [native(m) for m in mapped_results]
}

with open(os.path.join(OUTPUT_DIR, "summary.json"), "w") as f:
    json.dump(summary, f, indent=2)

print("Done. Outputs in:", OUTPUT_DIR)
print("Mapped results:", [ (m['question_number'], m['option_number'], m['image_file']) for m in mapped_results ])
